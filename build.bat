@echo off
echo Windows Meeting Recorder - Build Script
echo ========================================

REM Check if dotnet is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 8.0 SDK is not installed or not in PATH
    echo.
    echo Please install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo Download the SDK version ^(not just runtime^)
    echo After installation, restart your command prompt and run this script again.
    pause
    exit /b 1
)

echo .NET SDK found. Version:
dotnet --version

echo.
echo Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    pause
    exit /b 1
)

echo.
echo Building application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Creating self-contained executable...
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo.
echo The application has been built to: ./publish/
echo You can run it directly: ./publish/WindowsMeetingRecorder.exe
echo.
echo The self-contained version includes all dependencies
echo and doesn't require .NET to be installed on target machines.
echo ========================================
pause
