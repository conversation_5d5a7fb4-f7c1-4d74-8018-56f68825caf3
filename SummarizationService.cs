using System.Net.Http;
using System.Text;
using Newtonsoft.Json;

namespace WindowsMeetingRecorder;

public class SummarizationService
{
    private readonly HttpClient _httpClient;
    private readonly string _ollamaUrl;

    public SummarizationService()
    {
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromMinutes(5);
        _ollamaUrl = "http://localhost:11434";
    }

    public async Task<string> SummarizeTextAsync(string text, IProgress<string>? progress = null)
    {
        try
        {
            progress?.Report("Checking Ollama connection...");

            // Check if Ollama is available
            if (!await IsOllamaAvailableAsync())
            {
                throw new Exception("Ollama is not running. Please start Ollama and ensure a model like 'llama3' is available.");
            }

            progress?.Report("Preparing summarization request...");

            var prompt = $@"Please analyze the following meeting transcript and provide a comprehensive summary:

**TRANSCRIPT:**
{text}

**Please provide:**
1. A brief summary (2-3 sentences)
2. Key points discussed
3. Any action items or decisions made
4. Important participants mentioned

Keep the response concise but informative.";

            var request = new
            {
                model = "llama3",
                prompt = prompt,
                stream = false,
                options = new
                {
                    temperature = 0.7,
                    max_tokens = 1000
                }
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            progress?.Report("Sending request to Ollama...");

            var response = await _httpClient.PostAsync($"{_ollamaUrl}/api/generate", content);
            
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Ollama request failed: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var ollamaResponse = JsonConvert.DeserializeObject<OllamaResponse>(responseContent);

            if (string.IsNullOrEmpty(ollamaResponse?.Response))
            {
                throw new Exception("Empty response from Ollama");
            }

            progress?.Report("Summarization completed!");
            return ollamaResponse.Response;
        }
        catch (Exception ex)
        {
            progress?.Report($"Summarization failed: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> IsOllamaAvailableAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_ollamaUrl}/api/tags");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<List<string>> GetAvailableModelsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_ollamaUrl}/api/tags");
            if (!response.IsSuccessStatusCode) return new List<string>();

            var content = await response.Content.ReadAsStringAsync();
            var modelsResponse = JsonConvert.DeserializeObject<OllamaModelsResponse>(content);
            
            return modelsResponse?.Models?.Select(m => m.Name).ToList() ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

// Helper classes for Ollama API responses
public class OllamaResponse
{
    public string? Response { get; set; }
}

public class OllamaModelsResponse
{
    public List<OllamaModel>? Models { get; set; }
}

public class OllamaModel
{
    public string Name { get; set; } = string.Empty;
}
