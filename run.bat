@echo off
echo Windows Meeting Recorder - Run Script
echo ======================================

REM Check if dotnet is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 8.0 SDK is not installed or not in PATH
    echo.
    echo Please install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo Alternatively, if you've already built the application,
    echo you can run the self-contained version:
    echo ./publish/WindowsMeetingRecorder.exe
    echo.
    pause
    exit /b 1
)

echo .NET SDK found. Version:
dotnet --version

echo.
echo Checking if application is built...
if not exist "bin\Release\net8.0-windows\WindowsMeetingRecorder.exe" (
    echo Application not built yet. Building now...
    call build.bat
    if %errorlevel% neq 0 (
        echo Build failed. Please check the errors above.
        pause
        exit /b 1
    )
)

echo.
echo Starting Windows Meeting Recorder...
echo.
echo The application will start minimized to the system tray.
echo Look for the application icon in your system tray ^(bottom-right corner^).
echo Right-click the tray icon to access the menu.
echo.

dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo Application exited with error code: %errorlevel%
    pause
)
