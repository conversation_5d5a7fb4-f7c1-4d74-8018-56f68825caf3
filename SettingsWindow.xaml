<Window x:Class="WindowsMeetingRecorder.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Transcription Settings" Height="600" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Transcription Provider Settings" 
                   FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Provider Selection -->
                <GroupBox Header="Preferred Provider" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <ComboBox x:Name="ProviderComboBox" Margin="0,5">
                            <ComboBoxItem Content="OpenAI Whisper API" Tag="OpenAI"/>
                            <ComboBoxItem Content="Azure Speech Services" Tag="Azure"/>
                            <ComboBoxItem Content="Local Whisper Model" Tag="LocalWhisper"/>
                            <ComboBoxItem Content="Auto (Try in order)" Tag="Auto"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>
                
                <!-- OpenAI Settings -->
                <GroupBox Header="OpenAI Whisper API" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TextBlock Text="API Key:" Margin="0,0,0,5"/>
                        <PasswordBox x:Name="OpenAIKeyBox" Margin="0,0,0,10"/>
                        <TextBlock Text="Get your API key from: https://platform.openai.com/api-keys" 
                                   FontSize="11" Foreground="Gray" TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- Azure Settings -->
                <GroupBox Header="Azure Speech Services" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TextBlock Text="Subscription Key:" Margin="0,0,0,5"/>
                        <PasswordBox x:Name="AzureKeyBox" Margin="0,0,0,10"/>
                        <TextBlock Text="Region:" Margin="0,0,0,5"/>
                        <TextBox x:Name="AzureRegionBox" Margin="0,0,0,10" 
                                 ToolTip="e.g., eastus, westus2, etc."/>
                        <TextBlock Text="Get your key from: https://portal.azure.com" 
                                   FontSize="11" Foreground="Gray" TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- Local Whisper Settings -->
                <GroupBox Header="Local Whisper Model" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <CheckBox x:Name="EnableLocalWhisperBox" Content="Enable Local Whisper" Margin="0,0,0,10"/>
                        <TextBlock Text="Model Path:" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="LocalModelPathBox" Grid.Column="0" Margin="0,0,5,0"/>
                            <Button x:Name="BrowseModelButton" Grid.Column="1" Content="Browse..." 
                                    Click="BrowseModelButton_Click" Padding="10,2"/>
                        </Grid>
                        <TextBlock Text="Download models from: https://huggingface.co/ggerganov/whisper.cpp" 
                                   FontSize="11" Foreground="Gray" TextWrapping="Wrap" Margin="0,5,0,0"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- Language Settings -->
                <GroupBox Header="Language Settings" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TextBlock Text="Language:" Margin="0,0,0,5"/>
                        <ComboBox x:Name="LanguageComboBox" Margin="0,0,0,5">
                            <ComboBoxItem Content="English (US)" Tag="en-US"/>
                            <ComboBoxItem Content="English (UK)" Tag="en-GB"/>
                            <ComboBoxItem Content="Spanish" Tag="es-ES"/>
                            <ComboBoxItem Content="French" Tag="fr-FR"/>
                            <ComboBoxItem Content="German" Tag="de-DE"/>
                            <ComboBoxItem Content="Italian" Tag="it-IT"/>
                            <ComboBoxItem Content="Portuguese" Tag="pt-PT"/>
                            <ComboBoxItem Content="Russian" Tag="ru-RU"/>
                            <ComboBoxItem Content="Japanese" Tag="ja-JP"/>
                            <ComboBoxItem Content="Chinese (Simplified)" Tag="zh-CN"/>
                        </ComboBox>
                    </StackPanel>
                </GroupBox>
                
                <!-- Status -->
                <GroupBox Header="Current Status" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TextBlock x:Name="StatusTextBlock" TextWrapping="Wrap" 
                                   FontFamily="Consolas" FontSize="11"/>
                    </StackPanel>
                </GroupBox>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="Test Configuration" x:Name="TestButton" Click="TestButton_Click" 
                    Margin="0,0,10,0" Padding="15,5"/>
            <Button Content="Save" x:Name="SaveButton" Click="SaveButton_Click" 
                    Margin="0,0,10,0" Padding="15,5" IsDefault="True"/>
            <Button Content="Cancel" x:Name="CancelButton" Click="CancelButton_Click" 
                    Padding="15,5" IsCancel="True"/>
        </StackPanel>
        
    </Grid>
</Window>
