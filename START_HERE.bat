@echo off
title Windows Meeting Recorder - Setup Assistant
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                Windows Meeting Recorder                      ║
echo  ║                    Setup Assistant                           ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if dotnet is installed
echo [1/3] Checking for .NET 8.0 SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 8.0 SDK is NOT installed
    echo.
    echo 📥 Please install .NET 8.0 SDK first:
    echo    1. Go to: https://dotnet.microsoft.com/download/dotnet/8.0
    echo    2. Download ".NET 8.0 SDK" ^(not just runtime^)
    echo    3. Install it and restart this script
    echo.
    echo 🌐 Opening download page in your browser...
    start https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo Press any key after installing .NET 8.0 SDK...
    pause >nul
    goto :check_dotnet
) else (
    echo ✅ .NET 8.0 SDK found
    dotnet --version
)

:check_dotnet
echo.
echo [2/3] Checking project files...
if not exist "WindowsMeetingRecorder.csproj" (
    echo ❌ Project file not found
    echo Make sure you're running this from the WindowsMeetingRecorder folder
    pause
    exit /b 1
)
echo ✅ Project files found

echo.
echo [3/3] Building application...
echo This may take a few minutes on first run...
echo.

dotnet restore --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Failed to restore packages
    echo Check your internet connection and try again
    pause
    exit /b 1
)

dotnet build --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Build failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo ✅ Build completed successfully!
echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                     🎉 Ready to Run! 🎉                     ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.
echo The Windows Meeting Recorder is now ready to use!
echo.
echo 🚀 Starting the application...
echo    • The app will start minimized to your system tray
echo    • Look for the icon in the bottom-right corner
echo    • Right-click the tray icon to access the menu
echo.
echo 📋 First-time setup:
echo    • Go to Settings to configure API keys ^(optional^)
echo    • Select meeting applications to monitor
echo    • Configure recording preferences
echo.
echo Press any key to start the application...
pause >nul

echo.
echo Starting Windows Meeting Recorder...
dotnet run --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application exited with an error
    echo Check the error messages above
    pause
)
