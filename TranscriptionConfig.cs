using System.IO;
using Newtonsoft.Json;

namespace WindowsMeetingRecorder;

public class TranscriptionConfig
{
    public string OpenAIApiKey { get; set; } = "";
    public string AzureSpeechKey { get; set; } = "";
    public string AzureSpeechRegion { get; set; } = "";
    public TranscriptionProvider PreferredProvider { get; set; } = TranscriptionProvider.OpenAI;
    public string Language { get; set; } = "en-US";
    public bool EnableLocalWhisper { get; set; } = false;
    public string LocalWhisperModelPath { get; set; } = "";

    private static readonly string ConfigPath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
        "WindowsMeetingRecorder", "config.json");

    public static TranscriptionConfig Load()
    {
        try
        {
            if (File.Exists(ConfigPath))
            {
                var json = File.ReadAllText(ConfigPath);
                return JsonConvert.DeserializeObject<TranscriptionConfig>(json) ?? new TranscriptionConfig();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading config: {ex.Message}");
        }
        
        return new TranscriptionConfig();
    }

    public void Save()
    {
        try
        {
            Directory.CreateDirectory(Path.GetDirectoryName(ConfigPath)!);
            var json = JsonConvert.SerializeObject(this, Formatting.Indented);
            File.WriteAllText(ConfigPath, json);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving config: {ex.Message}");
        }
    }

    public string GetConfigPath() => ConfigPath;
}

public enum TranscriptionProvider
{
    OpenAI,
    Azure,
    LocalWhisper,
    Auto // Try providers in order of preference
}
