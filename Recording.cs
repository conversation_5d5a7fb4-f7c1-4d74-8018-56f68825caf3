using System;
using System.ComponentModel.DataAnnotations;

namespace WindowsMeetingRecorder;

public class Recording
{
    [Key]
    public int Id { get; set; }
    
    [Required]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    public DateTime CreatedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    
    [Required]
    public TimeSpan Duration { get; set; }
    
    [Required]
    public RecordingStatus Status { get; set; }
    
    [Required]
    public string AudioFilePath { get; set; } = string.Empty;
    
    public string? TranscriptText { get; set; }
    
    public string? SummaryText { get; set; }
    
    public DateTime? TranscribedAt { get; set; }
    
    public DateTime? SummarizedAt { get; set; }
    
    public long AudioFileSize { get; set; }
    
    public string? ErrorMessage { get; set; }
}

public enum RecordingStatus
{
    Recording,
    Completed,
    Transcribing,
    Summarizing,
    Ready,
    Failed
}
