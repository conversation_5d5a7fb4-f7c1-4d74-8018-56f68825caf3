<Window x:Class="WindowsMeetingRecorder.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Windows Meeting Recorder - Basic Version" 
        Height="400" 
        Width="600"
        WindowStartupLocation="CenterScreen">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Windows Meeting Recorder" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>

        <!-- Status -->
        <TextBlock Grid.Row="1" 
                   Name="StatusText"
                   Text="Ready to record" 
                   FontSize="16" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>

        <!-- Controls -->
        <StackPanel Grid.Row="2" Orientation="Vertical" HorizontalAlignment="Center" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                <Button Name="RecordButton"
                        Content="Start Recording"
                        Click="RecordButton_Click"
                        Padding="20,10"
                        Margin="0,0,10,0"
                        Background="#007ACC"
                        Foreground="White"
                        BorderThickness="0"/>
                <Button Name="StopButton"
                        Content="Stop Recording"
                        Click="StopButton_Click"
                        Padding="20,10"
                        Margin="0,0,10,0"
                        IsEnabled="False"
                        Background="#DC3545"
                        Foreground="White"
                        BorderThickness="0"/>
                <Button Name="SettingsButton"
                        Content="⚙️ Settings"
                        Click="SettingsButton_Click"
                        Padding="15,10"
                        Background="#6C757D"
                        Foreground="White"
                        BorderThickness="0"
                        ToolTip="Configure transcription providers"/>
            </StackPanel>

            <!-- Processing Options -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <CheckBox Name="AutoTranscribeCheckBox"
                          Content="Auto-transcribe"
                          IsChecked="True"
                          Margin="0,0,20,0"/>
                <CheckBox Name="AutoSummarizeCheckBox"
                          Content="Auto-summarize"
                          IsChecked="True"/>
            </StackPanel>
        </StackPanel>

        <!-- Processing Status and Results -->
        <ScrollViewer Grid.Row="3" VerticalScrollBarVisibility="Auto" Margin="0,0,0,20">
            <StackPanel>
                <!-- Progress Section -->
                <Border Background="#F8F9FA" Padding="15" CornerRadius="5" Margin="0,0,0,15"
                        Name="ProgressPanel" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Name="ProgressText" Text="Processing..." FontWeight="Bold" Margin="0,0,0,10"/>
                        <ProgressBar Name="ProgressBar" Height="6" IsIndeterminate="True"/>
                    </StackPanel>
                </Border>

                <!-- Results Section -->
                <TabControl Name="ResultsTabControl" Visibility="Collapsed">
                    <TabItem Header="Transcript">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <TextBox Name="TranscriptTextBox"
                                     TextWrapping="Wrap"
                                     IsReadOnly="True"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Padding="10"/>
                        </ScrollViewer>
                    </TabItem>
                    <TabItem Header="Summary">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <TextBox Name="SummaryTextBox"
                                     TextWrapping="Wrap"
                                     IsReadOnly="True"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Padding="10"/>
                        </ScrollViewer>
                    </TabItem>
                </TabControl>

                <!-- Info Section (when no results) -->
                <TextBlock Name="InfoTextBlock"
                           TextWrapping="Wrap"
                           VerticalAlignment="Top">
                    <Run Text="Windows Meeting Recorder with AI Features" FontWeight="Bold"/>
                    <LineBreak/><LineBreak/>
                    <Run Text="Features:" FontWeight="Bold"/>
                    <LineBreak/>
                    <Run Text="• System audio recording using NAudio"/>
                    <LineBreak/>
                    <Run Text="• Local transcription using Whisper"/>
                    <LineBreak/>
                    <Run Text="• AI summarization using Ollama"/>
                    <LineBreak/>
                    <Run Text="• Privacy-first local processing"/>
                    <LineBreak/><LineBreak/>
                    <Run Text="Setup Requirements:" FontWeight="Bold"/>
                    <LineBreak/>
                    <Run Text="1. Download Whisper model (ggml-base.bin) to Models folder"/>
                    <LineBreak/>
                    <Run Text="2. Install and run Ollama with llama3 model"/>
                    <LineBreak/>
                    <Run Text="3. Start recording to test the features"/>
                </TextBlock>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <TextBlock Grid.Row="4" 
                   Text="Based on the Mac Recap application - Windows version" 
                   FontSize="12" 
                   Foreground="Gray" 
                   HorizontalAlignment="Center"/>
    </Grid>
</Window>
