using System.IO;
using Microsoft.EntityFrameworkCore;

namespace WindowsMeetingRecorder;

public class RecordingDbContext : DbContext
{
    public DbSet<Recording> Recordings { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
            "WindowsMeetingRecorder", "recordings.db");
        
        Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);
        
        optionsBuilder.UseSqlite($"Data Source={dbPath}");
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Recording>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
            entity.Property(e => e.AudioFilePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.ErrorMessage).HasMaxLength(1000);
            
            // Configure enum as string
            entity.Property(e => e.Status)
                .HasConversion<string>()
                .HasMaxLength(50);
            
            // Configure TimeSpan as ticks
            entity.Property(e => e.Duration)
                .HasConversion(
                    v => v.Ticks,
                    v => new TimeSpan(v));
            
            // Indexes for better performance
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.Status);
        });
    }
}
