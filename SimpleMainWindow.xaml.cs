using System.IO;
using System.Windows;
using NAudio.Wave;
using Microsoft.EntityFrameworkCore;

namespace WindowsMeetingRecorder;

public partial class SimpleMainWindow : Window
{
    private WasapiLoopbackCapture? _capture;
    private WaveFileWriter? _writer;
    private bool _isRecording = false;
    private readonly TranscriptionService _transcriptionService;
    private readonly SummarizationService _summarizationService;
    private readonly RecordingDbContext _dbContext;
    private Recording? _currentRecording;

    public SimpleMainWindow()
    {
        InitializeComponent();

        _transcriptionService = new TranscriptionService();
        _summarizationService = new SummarizationService();
        _dbContext = new RecordingDbContext();

        // Initialize database
        _dbContext.Database.EnsureCreated();

        // Check setup status
        CheckSetupStatus();
    }

    private async void CheckSetupStatus()
    {
        try
        {
            var status = new List<string>();

            // Check transcription providers
            if (_transcriptionService.IsModelAvailable())
            {
                status.Add("✅ Transcription configured");
            }
            else
            {
                status.Add("⚠️ No transcription providers configured");
            }

            if (await _summarizationService.IsOllamaAvailableAsync())
            {
                status.Add("✅ Ollama available");
            }
            else
            {
                status.Add("⚠️ Ollama not running");
            }

            StatusText.Text = string.Join(" | ", status);
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Setup check failed: {ex.Message}";
        }
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var settingsWindow = new SettingsWindow(_transcriptionService)
            {
                Owner = this
            };

            if (settingsWindow.ShowDialog() == true)
            {
                // Refresh status after settings change
                CheckSetupStatus();
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Error opening settings: {ex.Message}", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void RecordButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_isRecording) return;

            // Create output directory
            var outputDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WindowsMeetingRecorder");
            Directory.CreateDirectory(outputDir);

            // Create output file
            var fileName = $"Recording_{DateTime.Now:yyyyMMdd_HHmmss}.wav";
            var filePath = Path.Combine(outputDir, fileName);

            // Create recording record in database
            _currentRecording = new Recording
            {
                Title = $"Recording {DateTime.Now:yyyy-MM-dd HH:mm}",
                CreatedAt = DateTime.Now,
                Status = RecordingStatus.Recording,
                AudioFilePath = filePath
            };

            _dbContext.Recordings.Add(_currentRecording);
            await _dbContext.SaveChangesAsync();

            // Initialize WASAPI loopback capture
            _capture = new WasapiLoopbackCapture();
            _writer = new WaveFileWriter(filePath, _capture.WaveFormat);

            _capture.DataAvailable += (s, args) =>
            {
                _writer?.Write(args.Buffer, 0, args.BytesRecorded);
            };

            _capture.RecordingStopped += async (s, args) =>
            {
                _writer?.Dispose();
                _writer = null;
                _capture?.Dispose();
                _capture = null;

                await Dispatcher.InvokeAsync(async () =>
                {
                    _isRecording = false;
                    RecordButton.IsEnabled = true;
                    StopButton.IsEnabled = false;

                    if (_currentRecording != null)
                    {
                        _currentRecording.CompletedAt = DateTime.Now;
                        _currentRecording.Duration = _currentRecording.CompletedAt.Value - _currentRecording.CreatedAt;
                        _currentRecording.Status = RecordingStatus.Completed;
                        _currentRecording.AudioFileSize = new FileInfo(filePath).Length;

                        await _dbContext.SaveChangesAsync();

                        StatusText.Text = "Recording completed!";

                        // Always process if AI features are enabled
                        if (AutoTranscribeCheckBox.IsChecked == true || AutoSummarizeCheckBox.IsChecked == true)
                        {
                            StatusText.Text = "Recording completed. Starting AI processing...";
                            _ = ProcessRecordingAsync(_currentRecording);
                        }
                    }
                });
            };

            _capture.StartRecording();
            _isRecording = true;
            RecordButton.IsEnabled = false;
            StopButton.IsEnabled = true;
            StatusText.Text = "Recording... (capturing system audio)";

            // Hide results and show info
            ResultsTabControl.Visibility = Visibility.Collapsed;
            InfoTextBlock.Visibility = Visibility.Visible;
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Error starting recording: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void StopButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _capture?.StopRecording();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Error stopping recording: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task ProcessRecordingAsync(Recording recording)
    {
        try
        {
            // Show progress on UI thread
            await Dispatcher.InvokeAsync(() =>
            {
                ProgressPanel.Visibility = Visibility.Visible;
                InfoTextBlock.Visibility = Visibility.Collapsed;
                ProgressText.Text = "Starting processing...";
            });

            // Check what to process
            bool shouldTranscribe = false;
            bool shouldSummarize = false;

            await Dispatcher.InvokeAsync(() =>
            {
                shouldTranscribe = AutoTranscribeCheckBox.IsChecked == true;
                shouldSummarize = AutoSummarizeCheckBox.IsChecked == true;
            });

            string transcript = "";

            // Transcription
            if (shouldTranscribe)
            {
                try
                {
                    await Dispatcher.InvokeAsync(() => ProgressText.Text = "Transcribing audio...");

                    recording.Status = RecordingStatus.Transcribing;
                    await _dbContext.SaveChangesAsync();

                    transcript = await _transcriptionService.TranscribeAudioAsync(recording.AudioFilePath);

                    recording.TranscriptText = transcript;
                    recording.TranscribedAt = DateTime.Now;
                    await _dbContext.SaveChangesAsync();

                    await Dispatcher.InvokeAsync(() =>
                    {
                        TranscriptTextBox.Text = transcript;
                        ProgressText.Text = "Transcription completed!";
                    });
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Transcription failed: {ex.Message}";
                    recording.ErrorMessage = errorMsg;
                    await Dispatcher.InvokeAsync(() => TranscriptTextBox.Text = errorMsg);
                }
            }

            // Summarization
            if (shouldSummarize && !string.IsNullOrEmpty(transcript))
            {
                try
                {
                    await Dispatcher.InvokeAsync(() => ProgressText.Text = "Creating summary...");

                    recording.Status = RecordingStatus.Summarizing;
                    await _dbContext.SaveChangesAsync();

                    var summary = await _summarizationService.SummarizeTextAsync(transcript);

                    recording.SummaryText = summary;
                    recording.SummarizedAt = DateTime.Now;
                    await _dbContext.SaveChangesAsync();

                    await Dispatcher.InvokeAsync(() =>
                    {
                        SummaryTextBox.Text = summary;
                        ProgressText.Text = "Summarization completed!";
                    });
                }
                catch (Exception ex)
                {
                    var errorMsg = $"Summarization failed: {ex.Message}";
                    recording.ErrorMessage += $" {errorMsg}";
                    await Dispatcher.InvokeAsync(() => SummaryTextBox.Text = errorMsg);
                }
            }

            // Mark as ready
            recording.Status = RecordingStatus.Ready;
            await _dbContext.SaveChangesAsync();

            await Dispatcher.InvokeAsync(() =>
            {
                ProgressPanel.Visibility = Visibility.Collapsed;
                ResultsTabControl.Visibility = Visibility.Visible;
                StatusText.Text = "Processing completed!";
            });
        }
        catch (Exception ex)
        {
            recording.Status = RecordingStatus.Failed;
            recording.ErrorMessage = ex.Message;
            await _dbContext.SaveChangesAsync();

            await Dispatcher.InvokeAsync(() =>
            {
                ProgressPanel.Visibility = Visibility.Collapsed;
                StatusText.Text = $"Processing failed: {ex.Message}";
            });
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        if (_isRecording)
        {
            _capture?.StopRecording();
        }

        _dbContext?.Dispose();
        _summarizationService?.Dispose();

        base.OnClosed(e);
    }
}
