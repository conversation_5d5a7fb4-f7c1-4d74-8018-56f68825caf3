{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Data Source=recordings.db"}, "Ollama": {"BaseUrl": "http://localhost:11434"}, "OpenRouter": {"BaseUrl": "https://openrouter.ai/api/v1", "ApiKey": ""}, "HuggingFace": {"Token": ""}, "AzureSpeech": {"SubscriptionKey": "", "Region": "", "Enabled": false}, "Audio": {"DefaultSampleRate": 44100, "DefaultBitDepth": 16, "DefaultChannels": 2}, "Transcription": {"DefaultModel": "base", "ModelsPath": "%APPDATA%\\WindowsMeetingRecorder\\Models"}, "Summarization": {"DefaultModel": "llama3", "DefaultProvider": "Ollama"}, "Storage": {"DefaultRecordingsPath": "%USERPROFILE%\\Documents\\WindowsMeetingRecorder\\Recordings", "MaxRecordingDays": 30, "DeleteAudioAfterProcessing": false}, "UI": {"StartMinimized": true, "ShowNotifications": true, "MinimizeToTray": true, "Theme": "System"}, "Privacy": {"LocalProcessingOnly": true, "EncryptStoredData": false}, "MeetingDetection": {"AutoDetect": true, "AutoStartRecording": false, "MonitoredApplications": ["Teams", "Zoom", "chrome", "msedge", "Skype", "Discord", "<PERSON><PERSON>ck"]}}