# Windows Meeting Recorder - Setup Guide

## Quick Start

### Step 1: Install .NET 8.0 SDK

1. **Download .NET 8.0 SDK** (not just runtime):
   - Go to: https://dotnet.microsoft.com/download/dotnet/8.0
   - Click "Download .NET 8.0 SDK" (the larger download, ~200MB)
   - Run the installer and follow the prompts

2. **Verify Installation:**
   - Open a new Command Prompt or PowerShell
   - Run: `dotnet --version`
   - You should see something like: `8.0.xxx`

### Step 2: Build and Run

**Option A: Using the provided scripts (Easiest)**
```bash
# Double-click build.bat to build the application
# Then double-click run.bat to start it
```

**Option B: Using command line**
```bash
dotnet restore
dotnet build --configuration Release
dotnet run --configuration Release
```

**Option C: Create standalone executable**
```bash
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish
# Then run: ./publish/WindowsMeetingRecorder.exe
```

### Step 3: First-Time Setup

1. **The app starts minimized to system tray** (look in bottom-right corner)
2. **Right-click the tray icon** → "Show Window" to open the main interface
3. **Go to Settings** to configure:
   - API keys (optional)
   - Recording preferences
   - Meeting detection settings

## Prerequisites for Full Functionality

### For Local Transcription (Whisper)
- **Hugging Face Account** (free):
  1. Sign up at: https://huggingface.co/
  2. Go to: https://huggingface.co/settings/tokens
  3. Create a new token
  4. Add it in Settings → Transcription

### For Local Summarization (Recommended)
- **Install Ollama**:
  1. Download from: https://ollama.com/download
  2. Install and run: `ollama serve`
  3. Pull a model: `ollama pull llama3`
  4. Test in Settings → Summarization

### For Cloud Summarization (Optional)
- **OpenRouter Account** (paid):
  1. Sign up at: https://openrouter.ai/
  2. Get API key from dashboard
  3. Add it in Settings → Summarization

## System Requirements

### Minimum
- Windows 10 version 1903 or later
- 8 GB RAM
- 5 GB free disk space
- Audio system with WASAPI support

### Recommended
- Windows 11
- 16 GB RAM
- 20 GB free disk space (for models and recordings)
- SSD for better performance

## Troubleshooting

### "dotnet is not recognized"
- Install .NET 8.0 SDK (not just runtime)
- Restart your command prompt after installation
- Make sure you downloaded the SDK, not just the runtime

### "Build failed" or Package Restore Issues
- Make sure you have internet connection
- Try: `dotnet nuget locals all --clear`
- Then: `dotnet restore --force`

### Audio Recording Issues
- Run as Administrator if needed
- Check Windows audio permissions
- Make sure your audio devices are working

### Transcription Issues
- Verify Hugging Face token is correct
- Check available disk space (models are large)
- Try with smaller models first (base vs large)

### Summarization Issues
- For Ollama: Make sure `ollama serve` is running
- For OpenRouter: Verify API key and internet connection
- Check the logs in `%APPDATA%\WindowsMeetingRecorder\Logs`

## File Locations

After first run, the application creates:
- **Settings**: `%APPDATA%\WindowsMeetingRecorder\settings.json`
- **Database**: `%APPDATA%\WindowsMeetingRecorder\recordings.db`
- **Recordings**: `%USERPROFILE%\Documents\WindowsMeetingRecorder\Recordings`
- **Models**: `%APPDATA%\WindowsMeetingRecorder\Models`
- **Logs**: `%APPDATA%\WindowsMeetingRecorder\Logs`

## Usage Tips

1. **Start the app before your meetings** - it runs in the background
2. **Select your meeting application** from the dropdown
3. **Enable microphone** if you want to record your voice too
4. **Click record** or let it auto-detect meetings
5. **Processing happens automatically** after stopping recording

## Privacy Notes

- **All processing is local by default** - no data leaves your computer
- **API keys are encrypted** using Windows security
- **You can disable cloud services** entirely in Privacy settings
- **Recordings are stored locally** and never uploaded anywhere

## Getting Help

1. Check this setup guide
2. Look at the main README.md for detailed information
3. Check the logs in `%APPDATA%\WindowsMeetingRecorder\Logs`
4. Make sure all prerequisites are installed

## Development

If you want to modify the code:
1. Install Visual Studio 2022 Community (free)
2. Open `WindowsMeetingRecorder.csproj`
3. The project uses .NET 8, WPF, and Entity Framework Core
