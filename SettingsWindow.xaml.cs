using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;

namespace WindowsMeetingRecorder;

public partial class SettingsWindow : Window
{
    private TranscriptionConfig _config;
    private TranscriptionService _transcriptionService;

    public SettingsWindow(TranscriptionService transcriptionService)
    {
        InitializeComponent();
        _transcriptionService = transcriptionService;
        _config = transcriptionService.GetConfig();
        LoadSettings();
        UpdateStatus();
    }

    private void LoadSettings()
    {
        // Load provider selection
        foreach (ComboBoxItem item in ProviderComboBox.Items)
        {
            if (item.Tag.ToString() == _config.PreferredProvider.ToString())
            {
                ProviderComboBox.SelectedItem = item;
                break;
            }
        }

        // Load API keys (show masked)
        if (!string.IsNullOrEmpty(_config.OpenAIApiKey))
        {
            OpenAIKeyBox.Password = new string('*', Math.Min(_config.OpenAIApiKey.Length, 20));
        }

        if (!string.IsNullOrEmpty(_config.AzureSpeechKey))
        {
            AzureKeyBox.Password = new string('*', Math.Min(_config.AzureSpeechKey.Length, 20));
        }

        AzureRegionBox.Text = _config.AzureSpeechRegion;
        EnableLocalWhisperBox.IsChecked = _config.EnableLocalWhisper;
        LocalModelPathBox.Text = _config.LocalWhisperModelPath;

        // Load language selection
        foreach (ComboBoxItem item in LanguageComboBox.Items)
        {
            if (item.Tag.ToString() == _config.Language)
            {
                LanguageComboBox.SelectedItem = item;
                break;
            }
        }

        // Default selections if nothing is selected
        if (ProviderComboBox.SelectedItem == null)
            ProviderComboBox.SelectedIndex = 0;
        if (LanguageComboBox.SelectedItem == null)
            LanguageComboBox.SelectedIndex = 0;
    }

    private void UpdateStatus()
    {
        var status = new List<string>();

        if (!string.IsNullOrEmpty(_config.OpenAIApiKey))
            status.Add("✅ OpenAI API key configured");
        else
            status.Add("❌ OpenAI API key not set");

        if (!string.IsNullOrEmpty(_config.AzureSpeechKey) && !string.IsNullOrEmpty(_config.AzureSpeechRegion))
            status.Add("✅ Azure Speech Services configured");
        else
            status.Add("❌ Azure Speech Services not configured");

        if (_config.EnableLocalWhisper && !string.IsNullOrEmpty(_config.LocalWhisperModelPath) && File.Exists(_config.LocalWhisperModelPath))
            status.Add("✅ Local Whisper model available");
        else
            status.Add("❌ Local Whisper not configured");

        status.Add($"📍 Preferred provider: {_config.PreferredProvider}");
        status.Add($"🌐 Language: {_config.Language}");

        StatusTextBlock.Text = string.Join("\n", status);
    }

    private void BrowseModelButton_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = "Select Whisper Model File",
            Filter = "Whisper Models (*.bin)|*.bin|All Files (*.*)|*.*",
            CheckFileExists = true
        };

        if (dialog.ShowDialog() == true)
        {
            LocalModelPathBox.Text = dialog.FileName;
        }
    }

    private async void TestButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            TestButton.IsEnabled = false;
            TestButton.Content = "Testing...";

            // Save current settings temporarily
            var tempConfig = CreateConfigFromUI();
            _transcriptionService.UpdateConfig(tempConfig);

            // Test with a simple audio file (create a short beep)
            var testAudioPath = await CreateTestAudioFile();
            
            try
            {
                var result = await _transcriptionService.TranscribeAudioAsync(testAudioPath);
                System.Windows.MessageBox.Show($"Test successful!\n\nTranscription result: {result}",
                    "Configuration Test", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            finally
            {
                // Clean up test file
                if (File.Exists(testAudioPath))
                    File.Delete(testAudioPath);
            }
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Test failed:\n\n{ex.Message}",
                "Configuration Test", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            TestButton.IsEnabled = true;
            TestButton.Content = "Test Configuration";
        }
    }

    private void SaveButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var newConfig = CreateConfigFromUI();
            _transcriptionService.UpdateConfig(newConfig);
            
            System.Windows.MessageBox.Show("Settings saved successfully!", "Settings",
                MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Error saving settings:\n\n{ex.Message}", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private TranscriptionConfig CreateConfigFromUI()
    {
        var config = new TranscriptionConfig();

        // Provider selection
        if (ProviderComboBox.SelectedItem is ComboBoxItem providerItem)
        {
            Enum.TryParse<TranscriptionProvider>(providerItem.Tag.ToString(), out var provider);
            config.PreferredProvider = provider;
        }

        // API keys (only update if changed)
        if (!string.IsNullOrEmpty(OpenAIKeyBox.Password) && !OpenAIKeyBox.Password.StartsWith("*"))
        {
            config.OpenAIApiKey = OpenAIKeyBox.Password;
        }
        else if (!string.IsNullOrEmpty(_config.OpenAIApiKey))
        {
            config.OpenAIApiKey = _config.OpenAIApiKey; // Keep existing
        }

        if (!string.IsNullOrEmpty(AzureKeyBox.Password) && !AzureKeyBox.Password.StartsWith("*"))
        {
            config.AzureSpeechKey = AzureKeyBox.Password;
        }
        else if (!string.IsNullOrEmpty(_config.AzureSpeechKey))
        {
            config.AzureSpeechKey = _config.AzureSpeechKey; // Keep existing
        }

        config.AzureSpeechRegion = AzureRegionBox.Text;
        config.EnableLocalWhisper = EnableLocalWhisperBox.IsChecked == true;
        config.LocalWhisperModelPath = LocalModelPathBox.Text;

        // Language selection
        if (LanguageComboBox.SelectedItem is ComboBoxItem langItem)
        {
            config.Language = langItem.Tag.ToString() ?? "en-US";
        }

        return config;
    }

    private Task<string> CreateTestAudioFile()
    {
        return Task.Run(() =>
        {
            // Create a simple test audio file (1 second of silence)
            var testPath = Path.Combine(Path.GetTempPath(), $"test_audio_{Guid.NewGuid()}.wav");

            // Create a 1-second silent WAV file for testing
            var sampleRate = 16000;
            var duration = 1.0; // 1 second
            var samples = (int)(sampleRate * duration);

            using var writer = new NAudio.Wave.WaveFileWriter(testPath, new NAudio.Wave.WaveFormat(sampleRate, 16, 1));
            var buffer = new byte[samples * 2]; // 16-bit samples
            writer.Write(buffer, 0, buffer.Length);

            return testPath;
        });
    }
}
