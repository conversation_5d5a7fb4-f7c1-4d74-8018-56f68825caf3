using System.IO;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using NAudio.Wave;
using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;

namespace WindowsMeetingRecorder;

public class TranscriptionService
{
    private readonly HttpClient _httpClient;
    private readonly TranscriptionConfig _config;

    public TranscriptionService()
    {
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromMinutes(10);
        _config = TranscriptionConfig.Load();
    }

    public async Task<string> TranscribeAudioAsync(string audioFilePath, IProgress<string>? progress = null)
    {
        try
        {
            progress?.Report("Starting transcription...");

            // Try providers based on configuration and availability
            switch (_config.PreferredProvider)
            {
                case TranscriptionProvider.OpenAI:
                    if (!string.IsNullOrEmpty(_config.OpenAIApiKey))
                        return await TranscribeWithOpenAIAsync(audioFilePath, progress);
                    break;

                case TranscriptionProvider.Azure:
                    if (!string.IsNullOrEmpty(_config.AzureSpeechKey))
                        return await TranscribeWithAzureAsync(audioFilePath, progress);
                    break;

                case TranscriptionProvider.LocalWhisper:
                    if (_config.EnableLocalWhisper && !string.IsNullOrEmpty(_config.LocalWhisperModelPath))
                        return await TranscribeWithLocalWhisperAsync(audioFilePath, progress);
                    break;

                case TranscriptionProvider.Auto:
                    return await TranscribeWithAutoProviderAsync(audioFilePath, progress);
            }

            // Fallback: try auto provider selection
            return await TranscribeWithAutoProviderAsync(audioFilePath, progress);
        }
        catch (Exception ex)
        {
            progress?.Report($"Transcription failed: {ex.Message}");
            throw;
        }
    }

    private async Task<string> TranscribeWithAutoProviderAsync(string audioFilePath, IProgress<string>? progress)
    {
        progress?.Report("Auto-selecting transcription provider...");

        // Try providers in order of preference
        var providers = new[]
        {
            (TranscriptionProvider.OpenAI, !string.IsNullOrEmpty(_config.OpenAIApiKey)),
            (TranscriptionProvider.Azure, !string.IsNullOrEmpty(_config.AzureSpeechKey)),
            (TranscriptionProvider.LocalWhisper, _config.EnableLocalWhisper && !string.IsNullOrEmpty(_config.LocalWhisperModelPath))
        };

        foreach (var (provider, isAvailable) in providers)
        {
            if (!isAvailable) continue;

            try
            {
                progress?.Report($"Trying {provider} transcription...");

                return provider switch
                {
                    TranscriptionProvider.OpenAI => await TranscribeWithOpenAIAsync(audioFilePath, progress),
                    TranscriptionProvider.Azure => await TranscribeWithAzureAsync(audioFilePath, progress),
                    TranscriptionProvider.LocalWhisper => await TranscribeWithLocalWhisperAsync(audioFilePath, progress),
                    _ => throw new NotSupportedException($"Provider {provider} not supported")
                };
            }
            catch (Exception ex)
            {
                progress?.Report($"{provider} failed: {ex.Message}. Trying next provider...");
                continue;
            }
        }

        throw new InvalidOperationException("No transcription providers are configured or available. Please configure at least one provider in the settings.");
    }

    private async Task<string> TranscribeWithOpenAIAsync(string audioFilePath, IProgress<string>? progress)
    {
        progress?.Report("Uploading to OpenAI Whisper...");

        // Convert audio to compatible format if needed
        var convertedPath = await ConvertAudioForApiAsync(audioFilePath);

        try
        {
            using var form = new MultipartFormDataContent();
            using var fileStream = File.OpenRead(convertedPath);
            using var fileContent = new StreamContent(fileStream);

            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("audio/wav");
            form.Add(fileContent, "file", Path.GetFileName(convertedPath));
            form.Add(new StringContent("whisper-1"), "model");
            form.Add(new StringContent(_config.Language.Split('-')[0]), "language"); // Extract language code
            form.Add(new StringContent("json"), "response_format");

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.OpenAIApiKey);

            progress?.Report("Processing with OpenAI Whisper...");
            var response = await _httpClient.PostAsync("https://api.openai.com/v1/audio/transcriptions", form);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"OpenAI API error ({response.StatusCode}): {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<OpenAITranscriptionResponse>(responseContent);

            progress?.Report("OpenAI transcription completed!");
            return result?.Text ?? "Transcription failed - no text returned";
        }
        finally
        {
            // Clean up converted file if it's different from original
            if (convertedPath != audioFilePath && File.Exists(convertedPath))
            {
                try { File.Delete(convertedPath); } catch { }
            }
        }
    }

    private async Task<string> TranscribeWithAzureAsync(string audioFilePath, IProgress<string>? progress)
    {
        progress?.Report("Starting Azure Speech Services transcription...");

        var config = SpeechConfig.FromSubscription(_config.AzureSpeechKey, _config.AzureSpeechRegion);
        config.SpeechRecognitionLanguage = _config.Language;

        // Convert audio to compatible format
        var convertedPath = await ConvertAudioForApiAsync(audioFilePath);

        try
        {
            using var audioInput = AudioConfig.FromWavFileInput(convertedPath);
            using var recognizer = new SpeechRecognizer(config, audioInput);

            var transcriptBuilder = new StringBuilder();
            var completionSource = new TaskCompletionSource<bool>();

            recognizer.Recognized += (s, e) =>
            {
                if (e.Result.Reason == ResultReason.RecognizedSpeech)
                {
                    transcriptBuilder.Append(e.Result.Text + " ");
                    progress?.Report($"Transcribing... ({transcriptBuilder.Length} characters)");
                }
            };

            recognizer.SessionStopped += (s, e) =>
            {
                completionSource.SetResult(true);
            };

            recognizer.Canceled += (s, e) =>
            {
                if (e.Reason == CancellationReason.Error)
                {
                    completionSource.SetException(new Exception($"Azure Speech error: {e.ErrorDetails}"));
                }
                else
                {
                    completionSource.SetResult(true);
                }
            };

            progress?.Report("Processing with Azure Speech Services...");
            await recognizer.StartContinuousRecognitionAsync();
            await completionSource.Task;
            await recognizer.StopContinuousRecognitionAsync();

            var result = transcriptBuilder.ToString().Trim();
            progress?.Report("Azure transcription completed!");

            return string.IsNullOrEmpty(result) ? "No speech detected in audio" : result;
        }
        finally
        {
            // Clean up converted file if it's different from original
            if (convertedPath != audioFilePath && File.Exists(convertedPath))
            {
                try { File.Delete(convertedPath); } catch { }
            }
        }
    }

    private async Task<string> TranscribeWithLocalWhisperAsync(string audioFilePath, IProgress<string>? progress)
    {
        progress?.Report("Starting local Whisper transcription...");

        // This would implement local Whisper model execution
        // For now, return a placeholder
        await Task.Delay(2000); // Simulate processing

        throw new NotImplementedException("Local Whisper transcription is not yet implemented. Please use OpenAI or Azure providers.");
    }

    private Task<string> ConvertAudioForApiAsync(string inputPath)
    {
        return Task.Run(() =>
        {
            try
            {
                // Create a temporary WAV file with API-compatible format
                var tempPath = Path.Combine(Path.GetTempPath(), $"api_audio_{Guid.NewGuid()}.wav");

                using var reader = new AudioFileReader(inputPath);

                // Convert to 16kHz mono WAV (standard for most APIs)
                var targetFormat = new WaveFormat(16000, 16, 1);
                using var resampler = new WaveFormatConversionStream(targetFormat, reader);

                WaveFileWriter.CreateWaveFile(tempPath, resampler);

                return tempPath;
            }
            catch
            {
                // If conversion fails, try the original file
                return inputPath;
            }
        });
    }

    private double GetAudioDuration(string audioFilePath)
    {
        try
        {
            using var reader = new AudioFileReader(audioFilePath);
            return reader.TotalTime.TotalSeconds;
        }
        catch
        {
            return 30.0; // Default duration
        }
    }

    public bool IsModelAvailable()
    {
        // Check if any transcription provider is configured
        return !string.IsNullOrEmpty(_config.OpenAIApiKey) ||
               !string.IsNullOrEmpty(_config.AzureSpeechKey) ||
               (_config.EnableLocalWhisper && !string.IsNullOrEmpty(_config.LocalWhisperModelPath));
    }

    public string GetModelsPath()
    {
        var availableProviders = new List<string>();

        if (!string.IsNullOrEmpty(_config.OpenAIApiKey))
            availableProviders.Add("OpenAI Whisper");

        if (!string.IsNullOrEmpty(_config.AzureSpeechKey))
            availableProviders.Add("Azure Speech");

        if (_config.EnableLocalWhisper && !string.IsNullOrEmpty(_config.LocalWhisperModelPath))
            availableProviders.Add("Local Whisper");

        return availableProviders.Count > 0
            ? $"Available: {string.Join(", ", availableProviders)}"
            : "No transcription providers configured";
    }

    public TranscriptionConfig GetConfig() => _config;

    public void UpdateConfig(TranscriptionConfig newConfig)
    {
        _config.OpenAIApiKey = newConfig.OpenAIApiKey;
        _config.AzureSpeechKey = newConfig.AzureSpeechKey;
        _config.AzureSpeechRegion = newConfig.AzureSpeechRegion;
        _config.PreferredProvider = newConfig.PreferredProvider;
        _config.Language = newConfig.Language;
        _config.EnableLocalWhisper = newConfig.EnableLocalWhisper;
        _config.LocalWhisperModelPath = newConfig.LocalWhisperModelPath;
        _config.Save();
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

// Helper class for OpenAI API response
public class OpenAITranscriptionResponse
{
    public string Text { get; set; } = string.Empty;
}
