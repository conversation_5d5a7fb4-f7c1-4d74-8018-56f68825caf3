# Windows Meeting Recorder

A privacy-first, Windows-native meeting recorder and summarizer inspired by the Mac Recap application. Records system audio from meeting applications, transcribes locally using Whisper, and summarizes using local LLMs via Ollama.

## Features

- **Privacy-First**: All processing happens locally by default
- **Audio Recording**: Records system audio and optional microphone input using WASAPI
- **Meeting Detection**: Automatically detects Teams, Zoom, Google Meet, and other meeting applications
- **Local Transcription**: Uses Whisper.NET for speech-to-text processing
- **Local Summarization**: Uses Ollama for meeting summaries (with optional OpenRouter fallback)
- **System Tray Integration**: Runs quietly in the background
- **Clean Architecture**: Modular design with dependency injection

## System Requirements

### Minimum Requirements
- **OS**: Windows 10 version 1903 or later
- **Framework**: .NET 8.0 Runtime
- **RAM**: 8 GB
- **Storage**: 5 GB free space
- **Audio**: Windows audio system with WASAPI support

### Recommended Requirements
- **OS**: Windows 11
- **Framework**: .NET 8.0 Runtime
- **RAM**: 16 GB or more
- **Storage**: 20 GB free space (for Whisper models and recordings)
- **GPU**: NVIDIA GPU with CUDA support (for faster transcription)

## Installation

### Prerequisites

1. **Install .NET 8.0 Runtime**
   - Download from: https://dotnet.microsoft.com/download/dotnet/8.0
   - Install the Desktop Runtime (includes ASP.NET Core)

2. **Install Ollama (for local summarization)**
   - Download from: https://ollama.com/download
   - Install and run: `ollama pull llama3`

3. **Optional: Get API Keys**
   - **Hugging Face Token**: Required for downloading Whisper models
     - Sign up at: https://huggingface.co/
     - Get token from: https://huggingface.co/settings/tokens
   - **OpenRouter API Key**: Optional, for cloud-based summarization
     - Sign up at: https://openrouter.ai/
     - Get API key from dashboard

### Build from Source

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd WindowsMeetingRecorder
   ```

2. **Build the application**
   ```bash
   dotnet build --configuration Release
   ```

3. **Run the application**
   ```bash
   dotnet run
   ```

### First-Time Setup

1. **Configure API Keys** (if using)
   - Open Settings from the system tray
   - Go to Summarization tab
   - Enter your Hugging Face Token and/or OpenRouter API Key

2. **Download Whisper Models**
   - The application will prompt you to download Whisper models
   - Recommended: Start with the "base" model (39 MB)
   - For better accuracy: Use "large" model (1.5 GB)

3. **Configure Ollama**
   - Ensure Ollama is running: `ollama serve`
   - Pull a model: `ollama pull llama3`
   - Test connection in Settings > Summarization

## Usage

### Basic Recording

1. **Select Application**: Choose a meeting application from the dropdown
2. **Optional**: Enable microphone recording
3. **Start Recording**: Click the record button or use system tray
4. **Stop Recording**: Click stop when the meeting ends

### Automatic Processing

After stopping a recording, the application will automatically:
1. **Transcribe** the audio using Whisper (if enabled)
2. **Summarize** the transcript using Ollama (if enabled)
3. **Notify** you when processing is complete

### Meeting Detection

Enable automatic meeting detection to:
- Automatically detect when meeting applications start
- Optionally start recording automatically
- Stop recording when meetings end

## Configuration

### Audio Settings
- **Sample Rate**: 22050, 44100, or 48000 Hz
- **Microphone**: Include microphone audio
- **Quality**: Balance between file size and quality

### Transcription Settings
- **Model**: tiny, base, small, medium, large
- **Language**: Auto-detect or specify language
- **Auto-transcribe**: Process recordings automatically

### Summarization Settings
- **Provider**: Ollama (local) or OpenRouter (cloud)
- **Model**: llama3, mistral, gpt-4, etc.
- **Custom Prompt**: Customize summary format
- **Auto-summarize**: Process transcripts automatically

### Privacy Settings
- **Local Processing Only**: Never send data to cloud services
- **Encrypt Stored Data**: Encrypt recordings and transcripts
- **Auto-delete**: Remove old recordings automatically

## File Locations

### Default Paths
- **Recordings**: `%USERPROFILE%\Documents\WindowsMeetingRecorder\Recordings`
- **Database**: `%APPDATA%\WindowsMeetingRecorder\recordings.db`
- **Settings**: `%APPDATA%\WindowsMeetingRecorder\settings.json`
- **Whisper Models**: `%APPDATA%\WindowsMeetingRecorder\Models`
- **Logs**: `%APPDATA%\WindowsMeetingRecorder\Logs`

### File Structure
```
WindowsMeetingRecorder/
├── Recordings/
│   ├── Teams_20250812_143022.system.wav
│   ├── Teams_20250812_143022.microphone.wav
│   └── ...
├── Models/
│   ├── ggml-base.bin
│   ├── ggml-large.bin
│   └── ...
├── recordings.db
├── settings.json
└── Logs/
```

## Troubleshooting

### Common Issues

1. **No Audio Recorded**
   - Check Windows audio permissions
   - Ensure WASAPI is available
   - Try running as administrator

2. **Transcription Fails**
   - Verify Whisper model is downloaded
   - Check available disk space
   - Ensure audio file is not corrupted

3. **Summarization Fails**
   - Verify Ollama is running: `ollama list`
   - Check network connection (for OpenRouter)
   - Verify API keys are correct

4. **Meeting Detection Not Working**
   - Check if applications are in monitored list
   - Verify applications have visible windows
   - Try refreshing the application list

### Performance Tips

1. **For Better Performance**
   - Use smaller Whisper models (base vs large)
   - Enable GPU acceleration if available
   - Close unnecessary applications during processing

2. **For Better Accuracy**
   - Use larger Whisper models
   - Ensure good audio quality
   - Use higher sample rates

## Privacy & Security

### Data Handling
- **Local First**: All processing happens on your device by default
- **No Telemetry**: No usage data is collected or transmitted
- **Encrypted Storage**: Optional encryption for sensitive recordings
- **Secure API Keys**: API keys are encrypted using Windows DPAPI

### Network Usage
- **Whisper Models**: Downloaded once from Hugging Face
- **Ollama**: Local processing, no network required
- **OpenRouter**: Only if explicitly enabled and configured

## Contributing

This project is based on the open-source Mac Recap application. Contributions are welcome!

### Development Setup
1. Install Visual Studio 2022 or VS Code with C# extension
2. Install .NET 8.0 SDK
3. Clone the repository
4. Open `WindowsMeetingRecorder.sln`

### Architecture
- **MVVM Pattern**: WPF with ViewModels
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Clean Architecture**: Separated concerns with services and repositories
- **Entity Framework**: SQLite database with Code First migrations

## License

This project follows the same license as the original Mac Recap application.

## Acknowledgments

- **Original Inspiration**: [Recap for Mac](https://github.com/RecapAI/Recap) by Rawand Ahmed Shaswar
- **Whisper**: OpenAI's Whisper speech recognition model
- **Ollama**: Local LLM runtime
- **NAudio**: Windows audio library

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the logs in `%APPDATA%\WindowsMeetingRecorder\Logs`
3. Create an issue with detailed information about your system and the problem
